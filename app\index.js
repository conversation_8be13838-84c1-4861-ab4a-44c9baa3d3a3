import React, { useEffect, useState } from "react";
import { Text, Image, View } from "react-native";
import { Colors, Fonts, Default } from "../constants/styles";
import MyStatusBar from "../components/myStatusBar";
import { useNavigation } from "expo-router";
import { StorageService } from "../utils/storage";

const SplashScreen = () => {
  const navigation = useNavigation();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkOnboardingAndNavigate = async () => {
      try {
        // Wait for splash screen display (2 seconds)
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if user has seen onboarding
        const hasSeenOnboarding = await StorageService.hasSeenOnboarding();
        
        if (hasSeenOnboarding) {
          // User has seen onboarding, go to login
          navigation.push("auth/loginScreen");
        } else {
          // First time user, show onboarding
          navigation.push("onboarding/onboardingScreen");
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to onboarding on error
        navigation.push("onboarding/onboardingScreen");
      } finally {
        setIsChecking(false);
      }
    };

    checkOnboardingAndNavigate();
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <MyStatusBar />
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: Colors.primary,
        }}
      >
        <Image
          source={require("../assets/images/splashIcon.png")}
          style={{ width: 78, height: 78 }}
        />
        <Text
          style={{
            ...Fonts.SemiBold25white,
            marginTop: Default.fixPadding * 0.5,
          }}
        >
          SafariBank
        </Text>
      </View>
    </View>
  );
};

export default SplashScreen;