import "react-native-gesture-handler";
import { useFonts } from "expo-font";
import { withTranslation } from "react-i18next";
import { Stack, useSegments } from "expo-router";
import { LogBox, StatusBar, AppState, View, Text } from "react-native";
import i18n from "../languages/index"; //don't remove this line
import { useEffect } from "react";
import { StorageService } from '../utils/storage';
import { useState } from 'react';
import notificationService from "../services/notificationService";
import inAppMessagingService from "../services/inAppMessagingService";
import installations from '@react-native-firebase/installations';

globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;

LogBox.ignoreAllLogs();

const MainNavigation = () => {
  const segments = useSegments();

  useEffect(() => {
    const subscription = AppState.addEventListener("change", (_) => {
      StatusBar.setBarStyle("light-content");
    });

    return () => {
      subscription.remove();
    };
  }, [segments]);

  return (
    <Stack screenOptions={{ headerShown: false, animation: "ios_from_right" }}>
      <Stack.Screen name="index" />
      <Stack.Screen
        name="onboarding/onboardingScreen"
        options={{ gestureEnabled: false }}
      />
      <Stack.Screen
        name="auth/loginScreen"
        options={{ gestureEnabled: false }}
      />
      <Stack.Screen name="auth/registerScreen" />
      <Stack.Screen name="auth/otpScreen" />
      <Stack.Screen name="auth/pinScreen" />
      <Stack.Screen name="(tabs)" options={{ gestureEnabled: false }} />
      <Stack.Screen name="services/servicesScreen" />
      <Stack.Screen name="accountDetail/accountDetailScreen" />
      <Stack.Screen name="latestTransaction/latestTransactionScreen" />
      <Stack.Screen name="notification/notificationScreen" />
      <Stack.Screen name="cardManagement/addNewCard" />
      <Stack.Screen name="fundTransfer/fundTransferScreen" />
      <Stack.Screen name="withdraw/withdrawScreen" />
      <Stack.Screen
        name="successfully/successfullyScreen"
        options={{ gestureEnabled: false }}
      />

      <Stack.Screen name="statement/statementScreen" />
      <Stack.Screen name="addDeposit/addDepositScreen" />

      <Stack.Screen name="loansStatement/loansStatementScreen" />
      <Stack.Screen name="educationLoan/educationLoanScreen" />
      <Stack.Screen name="editProfile/editProfileScreen" />
      <Stack.Screen name="editProfile/accountScreen" />
      <Stack.Screen name="nearBy/nearByScreen" />
      <Stack.Screen name="changePin/changePinScreen" />
      <Stack.Screen name="language/languageScreen" />
      <Stack.Screen name="termsCondition/termsConditionScreen" />
      <Stack.Screen name="privacyPolicy/privacyPolicyScreen" />
      <Stack.Screen name="customerSupport/customerSupportScreen" />
    </Stack>
  );
};

const ReloadAppOnLanguageChange = withTranslation("translation", {
  bindI18n: "languageChanged",
  bindStore: false,
})(MainNavigation);

export default function Layout() {
  const [fontsLoaded, fontError] = useFonts({
    Bold: require("../assets/fonts/NunitoSans-Bold.ttf"),
    SemiBold: require("../assets/fonts/NunitoSans-SemiBold.ttf"),
    Regular: require("../assets/fonts/NunitoSans-Regular.ttf"),
    ExtraBold: require("../assets/fonts/NunitoSans-ExtraBold.ttf"),
    Inter_SemiBold: require("../assets/fonts/Inter-SemiBold.ttf"),
  });


useEffect(() => {
  notificationService.initialize();
  inAppMessagingService.initialize();

  // Log Firebase Installation ID automatically
  if (__DEV__) {
    setTimeout(async () => {
      try {
        const installationId = await installations().getId();
        console.log('\n' + '='.repeat(80));
        console.log('🆔 FIREBASE INSTALLATION ID:');
        console.log('='.repeat(80));
        console.log(installationId);
        console.log('='.repeat(80));
        console.log('📋 Use this ID for Firebase In-App Messaging targeting');
        console.log('='.repeat(80) + '\n');
      } catch (error) {
        console.error('❌ Failed to get Firebase Installation ID:', error);
      }
    }, 2000); // Wait 2 seconds for Firebase to initialize
  }

  if (__DEV__) {
  global.getFCMToken = () => {
    const token = notificationService.getCurrentToken();
    console.log('\n' + '='.repeat(80));
    console.log('🎯 CURRENT FCM TOKEN:');
    console.log(token);
    console.log('='.repeat(80));
    return token;
  };

  global.getInstallationId = async () => {
    const id = await installations().getId();
    console.log('\n' + '='.repeat(80));
    console.log('🆔 FIREBASE INSTALLATION ID:');
    console.log(id);
    console.log('='.repeat(80));
    return id;
  };

  global.testNotification = () => {
    notificationService.sendLocalNotification(
      'Test Notification',
      'This is a test notification from SafariBank',
      { screen: 'home' }
    );
  };

  global.testInAppMessage = () => {
    inAppMessagingService.triggerTestMessage();
  };

  global.getInAppStatus = () => {
    const status = inAppMessagingService.getStatus();
    console.log('📱 In-App Messaging Status:', status);
    return status;
  };

  global.resetOnboarding = async () => {
    await StorageService.resetOnboarding();
    console.log('Onboarding reset - restart app to see onboarding again');
  };
}


  // Cleanup on unmount
  return () => {
    notificationService.cleanup();
    inAppMessagingService.cleanup();
  };
}, []);

  if (!fontsLoaded && !fontError) {
    return null;
  }

  return <ReloadAppOnLanguageChange />;
}
