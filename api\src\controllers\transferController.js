// Mock bank data
const mockBanks = [
  { key: '1', name: 'Amana Bank', code: 'AMANA001' },
  { key: '2', name: 'Mkombozi Bank', code: 'MKOMBOZI001' },
  { key: '3', name: 'TCB Bank', code: 'TCB001' },
  { key: '4', name: 'Ecobank', code: 'ECOBANK001' },
  { key: '5', name: 'NMB Bank', code: 'NMB001' },
  { key: '6', name: 'CRDB Bank', code: 'CRDB001' }
];

const initiateTransfer = async (req, res) => {
  try {
    const userId = req.user.userId;
    const { 
      transferType, 
      fromAccount, 
      toAccount, 
      amount, 
      currency, 
      remarks, 
      transactionPin 
    } = req.body;

    // Validate required fields
    if (!transferType || !fromAccount || !toAccount || !amount || !transactionPin) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_REQUIRED_FIELDS',
          message: 'Missing required transfer fields'
        }
      });
    }

    // Validate transfer type
    if (!['safariBank', 'otherBanks', 'mobile'].includes(transferType)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_TRANSFER_TYPE',
          message: 'Invalid transfer type'
        }
      });
    }

    // Validate amount
    const transferAmount = parseFloat(amount);
    if (isNaN(transferAmount) || transferAmount <= 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_AMOUNT',
          message: 'Invalid transfer amount'
        }
      });
    }

    // Mock validation - check if sufficient balance
    const availableBalance = 15000; // This comes from database
    const transferFee = transferType === 'safariBank' ? 0 : 50;
    const totalDeduction = transferAmount + transferFee;

    if (totalDeduction > availableBalance) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INSUFFICIENT_FUNDS',
          message: 'Insufficient balance in source account',
          details: {
            availableBalance: availableBalance.toFixed(2),
            requestedAmount: transferAmount.toFixed(2),
            transferFee: transferFee.toFixed(2),
            totalRequired: totalDeduction.toFixed(2)
          }
        }
      });
    }

    // Generate transaction reference
    const transactionId = `TXN_${Date.now()}`;
    const referenceNumber = `REF_${Math.random().toString(36).substr(2, 9).toUpperCase()}`;
    const newBalance = (availableBalance - totalDeduction).toFixed(2);

    // Mock successful transfer
    res.json({
      success: true,
      data: {
        transactionId: transactionId,
        referenceNumber: referenceNumber,
        status: 'COMPLETED',
        transferDetails: {
          fromAccount: fromAccount.accountNumber,
          toAccount: toAccount.accountNumber,
          beneficiaryName: toAccount.beneficiaryName,
          amount: transferAmount.toFixed(2),
          currency: currency || 'TSH',
          transferFee: transferFee.toFixed(2),
          totalDeducted: totalDeduction.toFixed(2)
        },
        timestamp: new Date().toISOString(),
        newBalance: newBalance
      },
      message: 'Transfer completed successfully'
    });

  } catch (error) {
    console.error('Transfer error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred'
      }
    });
  }
};

const getBankList = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        banks: mockBanks
      },
      message: 'Bank list retrieved successfully'
    });

  } catch (error) {
    console.error('Bank list error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred'
      }
    });
  }
};

module.exports = {
  initiateTransfer,
  getBankList
};