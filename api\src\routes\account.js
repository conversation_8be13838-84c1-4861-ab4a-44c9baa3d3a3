const express = require('express');
const { getAccountSummary, getDashboardAnalytics } = require('../controllers/accountController');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// All account routes require authentication
router.use(authenticateToken);

router.get('/summary', getAccountSummary);
router.get('/analytics', getDashboardAnalytics);

module.exports = router;