Request Endpoint: POST /api/auth/login
{
  "loginCode": "123456",
  "deviceInfo": {
    "deviceId": "unique-device-identifier",
    "platform": "ios/android",
    "appVersion": "1.0.0"
  }
}
{
  "success": true,
  "data": {
    "userId": "user_12345",
    "sessionToken": "jwt_token_here",
    "userProfile": {
    "userId": "user_12345",
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "phoneNumber": "+************",
    "customerId": "CUST001",
    "dateOfBirth": "1985-05-15",
    "address": "123 Main St, Dar es Salaam",
    "idType": "PASSPORT",
    "idNumber": "A12345678",
    "profileImage": "https://api.example.com/profiles/user_12345.jpg",
    "lastLogin": "2024-01-20T08:30:45Z",
    "accountStatus": "ACTIVE",
    "preferences": {
      "language": "en",
      "notifications": true,
      "biometricLogin": true
    }
  },
    "accounts": [
      {
        "accountId": "ACC001",
        "accountType": "Saving Account",
        "accountNumber": "SB-*******1231",
        "balance": "15000.00",
        "currency": "TSH"
      },
      {
        "accountId": "ACC002", 
        "accountType": "Current Account",
        "accountNumber": "SB-*******1232",
        "balance": "5000.00",
        "currency": "TSH"
      }
    ]
  },
  "message": "Login successful"
}
{
  "success": false,
  "error": {
    "code": "INVALID_LOGIN_CODE",
    "message": "Invalid login code provided"
  }
}

Request Endpoint: POST /api/transfer/initiate
{
  "transferType": "safariBank|otherBanks|mobile",
  "fromAccount": {
    "accountId": "ACC001",
    "accountNumber": "SB-*******1231"
  },
  "toAccount": {
    "accountNumber": "SB-*******5678",
    "bankName": "Safari Bank",
    "bankCode": "SAFARI001",
    "beneficiaryName": "Jane Doe",
    "iban": "*********************" // for other banks
  },
  "amount": "1000.00",
  "currency": "TSH",
  "remarks": "Payment for services",
  "transactionPin": "1234"
}
{
  "success": true,
  "data": {
    "transactionId": "TXN_789012345",
    "referenceNumber": "REF_ABC123",
    "status": "COMPLETED",
    "transferDetails": {
      "fromAccount": "SB-*******1231",
      "toAccount": "SB-*******5678",
      "beneficiaryName": "Jane Doe",
      "amount": "1000.00",
      "currency": "TSH",
      "transferFee": "50.00",
      "totalDeducted": "1050.00"
    },
    "timestamp": "2024-01-15T14:30:00Z",
    "newBalance": "13950.00"
  },
  "message": "Transfer completed successfully"
}
{
  "success": false,
  "error": {
    "code": "INSUFFICIENT_FUNDS",
    "message": "Insufficient balance in source account",
    "details": {
      "availableBalance": "500.00",
      "requestedAmount": "1000.00"
    }
  }
}

Request Endpoint: GET /api/statements/transactions
{
  "accountId": "ACC001",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "page": 1,
  "limit": 20,
  "transactionType": "all|credit|debit"
}
{
  "success": true,
  "data": {
    "accountInfo": {
      "accountId": "ACC001",
      "accountNumber": "SB-*******1231",
      "accountType": "Saving Account",
      "currentBalance": "15000.00",
      "currency": "TSH"
    },
    "summary": {
      "totalIncome": "3729.00",
      "totalExpenses": "347.00",
      "transactionCount": 25,
      "period": {
        "startDate": "2024-01-01",
        "endDate": "2024-01-31"
      }
    },
    "transactions": [
      {
        "transactionId": "TXN_001",
        "type": "DEBIT",
        "category": "TRANSFER",
        "description": "Money transfer to Jeklin shah",
        "amount": "-140.00",
        "balance": "14860.00",
        "timestamp": "2024-01-15T10:30:00Z",
        "referenceNumber": "REF_XYZ789",
        "counterparty": {
          "name": "Jeklin shah",
          "accountNumber": "SB-*******9999"
        }
      },
      {
        "transactionId": "TXN_002", 
        "type": "CREDIT",
        "category": "DEPOSIT",
        "description": "Paypal deposit",
        "amount": "+640.00",
        "balance": "15500.00",
        "timestamp": "2024-01-14T15:45:00Z",
        "referenceNumber": "REF_ABC456",
        "counterparty": {
          "name": "Paypal",
          "accountNumber": "PAYPAL_001"
        }
      },
      {
        "transactionId": "TXN_003",
        "type": "DEBIT", 
        "category": "MOBILE_PAYMENT",
        "description": "Mobile payment to +91 *********",
        "amount": "-150.00",
        "balance": "15350.00",
        "timestamp": "2024-01-13T09:20:00Z",
        "referenceNumber": "REF_MOB123",
        "counterparty": {
          "name": "+91 *********",
          "phoneNumber": "+91 *********"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalRecords": 25,
      "hasNextPage": true
    }
  },
  "message": "Statements retrieved successfully"
}

Request Endpoint: PUT /api/user/profile

Request Body:
{
  "fullName": "John M. Doe",
  "email": "<EMAIL>",
  "phoneNumber": "+************",
  "address": "456 Updated St, Dar es Salaam",
  "profileImage": "<base64_encoded_image>"
}

Response JSON (Success):
{
  "success": true,
  "data": {
    "userId": "user_12345",
    "updatedFields": ["fullName", "email", "address", "profileImage"]
  },
  "message": "Profile updated successfully"
}

Request Endpoint: POST /api/user/change-login-code

Request Body:
{
  "currentLoginCode": "123456",
  "newLoginCode": "123456",
  "confirmNewLoginCode": "123456"
}

Response JSON (Success):
{
  "success": true,
  "message": "Login code changed successfully"
}

Response JSON (Error - Incorrect Current Login Code):
{
  "success": false,
  "error": {
    "code": "INVALID_CURRENT_LOGIN_CODE",
    "message": "The current login code provided is incorrect"
  }
}

Request Endpoint: GET /api/terms

Response JSON (Success):
{
  "success": true,
  "data": {
    "version": "2.1.0",
    "lastUpdated": "2024-01-10T00:00:00Z",
    "content": "<h1>Terms and Conditions</h1><p>Full HTML content of the terms and conditions...</p>",
    "acceptanceRequired": true
  }
}

Request Endpoint: GET /api/privacy-policy

Response JSON (Success):
{
  "success": true,
  "data": {
    "version": "1.5.0",
    "lastUpdated": "2024-01-05T00:00:00Z",
    "content": "<h1>Privacy Policy</h1><p>Full HTML content of the privacy policy...</p>",
    "acceptanceRequired": true
  }
}

Request Endpoint: POST /api/legal/accept

Request Body:
{
  "documentType": "TERMS_AND_CONDITIONS|PRIVACY_POLICY|BOTH",
  "version": "2.1.0",
  "accept": true
}

Response JSON (Success):
{
  "success": true,
  "message": "Document(s) accepted successfully"
}

Request Endpoint: GET /api/notifications

Request Parameters:
{
  "userId": "user_12345",
  "page": 1,
  "limit": 20,
  "status": "all|read|unread"
}

Response JSON (Success):
{
  "success": true,
  "data": {
    "notifications": [
      {
        "notificationId": "NOTIF_001",
        "title": "Loan application approved!",
        "description": "Your car loan application has been successfully approved",
        "type": "LOAN_APPROVAL",
        "status": "UNREAD",
        "timestamp": "2024-01-15T09:36:00Z",
        "priority": "HIGH",
        "actionRequired": false,
        "metadata": {
          "loanId": "LOAN_12345",
          "amount": "50000.00"
        }
      },
      {
        "notificationId": "NOTIF_002", 
        "title": "Loan EMI period expires!",
        "description": "Your car loan EMI is due in 3 days",
        "type": "PAYMENT_REMINDER",
        "status": "UNREAD",
        "timestamp": "2024-01-14T10:30:00Z",
        "priority": "MEDIUM",
        "actionRequired": true,
        "metadata": {
          "loanId": "LOAN_12345",
          "dueDate": "2024-01-18",
          "amount": "800.00"
        }
      },
      {
        "notificationId": "NOTIF_003",
        "title": "Transfer completed successfully",
        "description": "Your transfer of TSH 1,000 to Jane Doe was completed",
        "type": "TRANSACTION_SUCCESS",
        "status": "READ",
        "timestamp": "2024-01-13T14:20:00Z",
        "priority": "LOW",
        "actionRequired": false,
        "metadata": {
          "transactionId": "TXN_789012345",
          "amount": "1000.00",
          "recipient": "Jane Doe"
        }
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 2,
      "totalRecords": 4,
      "hasNextPage": true
    }
  },
  "message": "Notifications retrieved successfully"
}

Response JSON (Error):
{
  "success": false,
  "error": {
    "code": "UNAUTHORIZED",
    "message": "Invalid session token"
  }
}

Request Endpoint: POST /api/download-statements
Request Body:
{
  "accountId": "ACC001",
  "startDate": "2024-01-01",
  "endDate": "2024-01-31",
  "format": "PDF|CSV"
}

Response JSON (Success):
{
  "success": true,
  "data": {
    "statementId": "STMT_12345",
    "fileName": "statements_2024-01-01_2024-01-31.pdf",
    "fileType": "PDF",
    "fileSize": "1.2 MB",
    "downloadUrl": "https://api.example.com/statements/STMT_12345.pdf"
  },
  "message": "Statement downloaded successfully"
}

Response JSON (Error):
{
  "success": false,
  "error": {
    "code": "INVALID_ACCOUNT_ID",
    "message": "Invalid account ID provided"
  }
}
