const jwt = require('jsonwebtoken');

// Mock user data (replace with database later)
const mockUsers = {
  '123456': {
    userId: 'user_12345',
    loginCode: '123456',
    userProfile: {
      userId: 'user_12345',
      fullName: '<PERSON>',
      email: '<EMAIL>',
      phoneNumber: '+************',
      customerId: 'CUST001',
      dateOfBirth: '1985-05-15',
      address: '123 Main St, Dar es Salaam',
      idType: 'PASSPORT',
      idNumber: 'A12345678',
      profileImage: 'https://api.example.com/profiles/user_12345.jpg',
      lastLogin: new Date().toISOString(),
      accountStatus: 'ACTIVE',
      preferences: {
        language: 'en',
        notifications: true,
        biometricLogin: true
      }
    },
    accounts: [
      {
        accountId: 'ACC001',
        accountType: 'Saving Account',
        accountNumber: 'SB-*******1231',
        balance: '15000.00',
        currency: 'TSH'
      },
      {
        accountId: 'ACC002',
        accountType: 'Current Account',
        accountNumber: 'SB-*******1232',
        balance: '5000.00',
        currency: 'TSH'
      }
    ]
  }
};

const login = async (req, res) => {
  try {
    const { loginCode, deviceInfo } = req.body;

    // Validate input
    if (!loginCode) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'MISSING_LOGIN_CODE',
          message: 'Login code is required'
        }
      });
    }

    // Check if user exists
    const user = mockUsers[loginCode];
    if (!user) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_LOGIN_CODE',
          message: 'Invalid login code provided'
        }
      });
    }

    // Generate JWT token
    const sessionToken = jwt.sign(
      { 
        userId: user.userId,
        loginCode: loginCode
      },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Update last login
    user.userProfile.lastLogin = new Date().toISOString();

    // Return success response
    res.json({
      success: true,
      data: {
        userId: user.userId,
        sessionToken: sessionToken,
        userProfile: user.userProfile,
        accounts: user.accounts
      },
      message: 'Login successful'
    });

  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred'
      }
    });
  }
};

module.exports = {
  login
};