// Mock account data (replace with database later)
const mockAccountData = {
  'user_12345': {
    accounts: [
      {
        accountId: 'ACC001',
        accountType: 'Saving Account',
        accountNumber: 'SB-*******1231',
        balance: '15000.00',
        currency: 'TSH'
      },
      {
        accountId: 'ACC002',
        accountType: 'Current Account',
        accountNumber: 'SB-*******1232',
        balance: '5000.00',
        currency: 'TSH'
      }
    ],
    analytics: {
      totalIncome: '3729.00',
      totalExpenses: '347.00',
      incomeChange: '17.3',
      expenseChange: '27.1',
      quickRatio: '1.38',
      profitMargin: '19',
      chartData: {
        labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
        datasets: [
          {
            data: [40, 30, 50, 20, 60, 70, 80, 50, 40, 30, 20, 10]
          }
        ]
      }
    }
  }
};

const getAccountSummary = async (req, res) => {
  try {
    const userId = req.user.userId;
    
    const userData = mockAccountData[userId];
    if (!userData) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User account data not found'
        }
      });
    }

    res.json({
      success: true,
      data: {
        accounts: userData.accounts,
        totalBalance: userData.accounts.reduce((sum, acc) => sum + parseFloat(acc.balance), 0).toFixed(2),
        currency: 'TSH'
      },
      message: 'Account summary retrieved successfully'
    });

  } catch (error) {
    console.error('Account summary error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred'
      }
    });
  }
};

const getDashboardAnalytics = async (req, res) => {
  try {
    const userId = req.user.userId;
    
    const userData = mockAccountData[userId];
    if (!userData) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User analytics data not found'
        }
      });
    }

    res.json({
      success: true,
      data: userData.analytics,
      message: 'Dashboard analytics retrieved successfully'
    });

  } catch (error) {
    console.error('Dashboard analytics error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'An internal error occurred'
      }
    });
  }
};

module.exports = {
  getAccountSummary,
  getDashboardAnalytics
};